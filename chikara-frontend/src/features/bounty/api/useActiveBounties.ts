import { useQuery } from "@tanstack/react-query";
import { orpc, QueryOptions } from "@/lib/orpc";

/**
 * Hook for fetching active bounties with more control
 */
export const useActiveBounties = (options?: QueryOptions) => {
    return useQuery(
        orpc.bounty.getActiveBountyList.queryOptions({
            staleTime: options?.staleTime || 30000,
            enabled: options?.enabled !== false,
        })
    );
};
