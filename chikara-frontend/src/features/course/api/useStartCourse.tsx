import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export interface StartCourseRequest {
    courseId: number;
}

export const useStartCourse = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.course.start.mutationOptions({
            onSuccess: () => {
                // Invalidate user info to update active course and cash
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                // Invalidate course list to update completion status
                queryClient.invalidateQueries({
                    queryKey: orpc.course.list.key(),
                });
            },
        })
    );
};
