import { useMutation, useQueryClient } from "@tanstack/react-query";
import { orpc } from "@/lib/orpc";
import { APIROUTES } from "@/helpers/apiRoutes";
import toast from "react-hot-toast";

/**
 * Hook for placing bounties
 */
export const usePlaceBounty = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.bounty.placeBounty.mutationOptions({
            onSuccess: () => {
                // Invalidate related queries
                queryClient.invalidateQueries({
                    queryKey: orpc.bounty.getActiveBountyList.key(),
                });

                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                toast.success("Bounty placed successfully!");

                if (onSuccessCallback) {
                    onSuccessCallback();
                }
            },
            onError: (error) => {
                const errorMessage = error.message || "Failed to place bounty";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );
};
