import { orpc, QueryOptions } from "@/lib/orpc";
import { useQuery } from "@tanstack/react-query";

/**
 * Custom hook to fetch user's status effects
 */
export const useGetStatusEffects = (options: QueryOptions = {}) => {
    return useQuery(
        orpc.user.getStatusEffects.queryOptions({
            staleTime: 30 * 1000, // 30 seconds
            ...options,
        })
    );
};

export default useGetStatusEffects;
