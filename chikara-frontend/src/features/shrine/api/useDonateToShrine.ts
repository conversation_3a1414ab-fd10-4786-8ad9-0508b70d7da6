import { useMutation, useQueryClient } from "@tanstack/react-query";
import { orpc } from "@/lib/orpc";
import { APIROUTES } from "@/helpers/apiRoutes";
import toast from "react-hot-toast";

export const useDonateToShrine = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.shrine.donate.mutationOptions({
            onSuccess: () => {
                // Invalidate shrine-related queries
                queryClient.invalidateQueries({ queryKey: orpc.shrine.getGoal.key() });
                queryClient.invalidateQueries({ queryKey: orpc.shrine.getDonations.key() });
                queryClient.invalidateQueries({ queryKey: orpc.shrine.getActiveBuff.key() });

                // Invalidate user data to update cash
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });

                toast.success("Donation Successful!");

                if (onSuccessCallback) {
                    onSuccessCallback();
                }
            },
            onError: (error) => {
                console.error("Shrine donation error:", error);
                toast.error(error.message || "Donation failed. Please try again.");
            },
        })
    );
};
