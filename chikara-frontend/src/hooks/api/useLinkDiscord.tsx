import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { APIROUTES } from "@/helpers/apiRoutes";

/**
 * Custom hook to link Discord account
 */
export const useLinkDiscord = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.user.linkDiscord.mutationOptions({
            onSuccess: () => {
                // Invalidate user info to refresh Discord link status
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });
                toast.success("Discord account linked successfully!");

                if (onSuccessCallback) {
                    onSuccessCallback();
                }
            },
            onError: (error: Error) => {
                toast.error(error.message || "Failed to link Discord account");
            },
        })
    );
};

export default useLinkDiscord;
