import { useQuery } from "@tanstack/react-query";
import { orpc, QueryOptions } from "@/lib/orpc";

export const useIsBuffActive = (buffName: string, options: QueryOptions = {}) => {
    return useQuery(
        orpc.shrine.isBuffActive.queryOptions({
            input: { buffName },
            staleTime: 60000, // 1 minute
            enabled: !!buffName,
            ...options,
        })
    );
};
