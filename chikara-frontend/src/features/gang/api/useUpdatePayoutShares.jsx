import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useUpdatePayoutShares = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        orpc.gang.updatePayoutShares.mutationOptions({
            onSuccess: () => {
                toast.success(`Payout shares updated successfully!`);
                queryClient.invalidateQueries({
                    queryKey: orpc.gang.getCurrentGang.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: orpc.gang.getMemberShares.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    const updatePayoutShares = (shares) => {
        if (!shares || !Array.isArray(shares)) {
            toast.error("Invalid shares data");
            return;
        }

        mutation.mutate({ shares });
    };

    return {
        updatePayoutShares,
        isLoading: mutation.isPending,
    };
};

export default useUpdatePayoutShares;
