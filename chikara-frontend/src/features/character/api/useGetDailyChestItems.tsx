import { orpc, QueryOptions } from "@/lib/orpc";
import { useQuery } from "@tanstack/react-query";

/**
 * Custom hook to fetch daily chest items
 */
const useGetDailyChestItems = (options: QueryOptions = {}) => {
    return useQuery(
        orpc.item.getDailyChestItems.queryOptions({
            staleTime: 300000, // 5 minutes
            ...options,
        })
    );
};

export default useGetDailyChestItems;
