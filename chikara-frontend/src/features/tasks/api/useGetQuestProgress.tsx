import { orpc, QueryOptions } from "@/lib/orpc";
import { useQuery } from "@tanstack/react-query";

interface UseGetQuestProgressOptions extends QueryOptions {
    activeOnly?: boolean;
}

const useGetQuestProgress = (options: UseGetQuestProgressOptions = {}) => {
    const { activeOnly = false, ...queryOptions } = options;
    
    return useQuery(
        orpc.quest.getProgress.queryOptions({
            input: { activeOnly },
            ...queryOptions,
        })
    );
};

export default useGetQuestProgress;
