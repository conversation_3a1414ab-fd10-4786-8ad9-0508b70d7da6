import { orpc, QueryOptions } from "@/lib/orpc";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

/**
 * Hook to get available polls for user
 */
export const useGetAvailablePolls = (options: QueryOptions = {}) => {
    return useQuery(
        orpc.suggestions.getAvailablePolls.queryOptions({
            ...options,
        })
    );
};

/**
 * Hook to get poll results
 */
export const useGetPollResults = (pollId: number, options: QueryOptions = {}) => {
    return useQuery(
        orpc.suggestions.getPollResults.queryOptions({
            input: { pollId },
            ...options,
        })
    );
};

/**
 * Hook to submit poll response
 */
export const useSubmitPollResponse = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.suggestions.submitPollResponse.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: orpc.suggestions.getAvailablePolls.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: orpc.suggestions.getPollResults.key(),
                });
                toast.success("Poll response submitted successfully!");
            },
            onError: (error) => {
                console.error("Submit poll response error:", error);
                toast.error(error.message || "Failed to submit poll response");
            },
        })
    );
};
