import { useMutation, useQueryClient } from "@tanstack/react-query";
import { orpc } from "@/lib/orpc";

export interface CompleteEpisodeRequest {
    episodeId: number;
    choices?: Record<string, string>;
}

/**
 * Hook to complete an episode
 */
export const useCompleteEpisode = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.story.completeEpisode.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: orpc.story.key() });
                // Also invalidate quests as story completion may affect quest progress
                queryClient.invalidateQueries({ queryKey: orpc.quest.key() });
                // Invalidate explore map to refresh story nodes
                queryClient.invalidateQueries({ queryKey: orpc.explore.key() });
            },
        })
    );
};
